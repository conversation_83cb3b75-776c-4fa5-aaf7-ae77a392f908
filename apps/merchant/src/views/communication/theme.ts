export const THEME_COLORS = {
  LIGHT_ORANGE: 'light_orange',
  LIGHT_YELLOW: 'light_yellow',
  BLUE: 'blue',
  LIGHT_RED: 'light_red',
  PINK: 'pink',
  PURPLE: 'purple',
  LIGHT_PINK: 'light_pink',
  <PERSON>Y<PERSON>: 'cyan',
  DARK_GRAY: 'dark_gray',
  ORANGE: 'orange',
} as const

export const THEME_COLOR_MAP = {
  [THEME_COLORS.LIGHT_ORANGE]: {
    name: 'Light Orange',
    backgroundColor: '#FE4C1C',
    color: '#ffffff',
  },
  [THEME_COLORS.LIGHT_YELLOW]: {
    name: 'Light Yellow',
    backgroundColor: '#fff453',
    color: '#fc7690',
  },
  [THEME_COLORS.BLUE]: {
    name: 'Blue',
    backgroundColor: '#6200f5',
    color: '#FFFFFF',
  },
  [THEME_COLORS.LIGHT_RED]: {
    name: 'Light Red',
    backgroundColor: '#E66608',
    color: '#FFFFFF',
  },
  [THEME_COLORS.PINK]: {
    name: 'Pink',
    backgroundColor: '#C34469',
    color: '#FFFFFF',
  },
  [THEME_COLORS.PURPLE]: {
    name: 'Purple',
    backgroundColor: '#574B90',
    color: '#FFFFFF',
  },
  [THEME_COLORS.LIGHT_PINK]: {
    name: 'Light Pink',
    backgroundColor: '#F78FA3',
    color: '#8B008B',
  },
  [THEME_COLORS.CYAN]: {
    name: 'Cyan',
    backgroundColor: '#3EC1D3',
    color: '#FFFFFF',
  },
  [THEME_COLORS.DARK_GRAY]: {
    name: 'Dark Gray',
    backgroundColor: '#303A52',
    color: '#FFFFFF',
  },
  [THEME_COLORS.ORANGE]: {
    name: 'Orange',
    backgroundColor: '#E26D42',
    color: '#FFFFFF',
  },
} as const

export const pickColor = (color: keyof typeof THEME_COLOR_MAP | string): { backgroundColor: string, color: string } => {
  return THEME_COLOR_MAP[color as keyof typeof THEME_COLOR_MAP] || { backgroundColor: color, color: '#ffffff' }
}

/**
 * 防抖和节流函数使用示例
 */

import { debounce, throttle, enhancedDebounce, enhancedThrottle } from './debounceThrottle'

// ===== 防抖函数使用示例 =====

// 1. 基础防抖 - 搜索输入框
const handleSearch = (query: string) => {
  console.log('搜索:', query)
  // 执行搜索逻辑
}

const debouncedSearch = debounce(handleSearch, 300)

// 使用示例
// debouncedSearch('vue')
// debouncedSearch('vue.js')  // 前一个调用被取消
// debouncedSearch('vue.js 3') // 前一个调用被取消，300ms后执行这个

// 2. 立即执行的防抖 - 按钮点击
const handleButtonClick = () => {
  console.log('按钮被点击')
}

const debouncedClick = debounce(handleButtonClick, 1000, true)

// 使用示例
// debouncedClick() // 立即执行
// debouncedClick() // 被忽略
// debouncedClick() // 被忽略，1秒后不会再执行

// ===== 节流函数使用示例 =====

// 1. 基础节流 - 滚动事件
const handleScroll = (event: Event) => {
  console.log('滚动位置:', window.scrollY)
}

const throttledScroll = throttle(handleScroll, 100)

// 使用示例
// window.addEventListener('scroll', throttledScroll)

// 2. 自定义节流选项 - 窗口大小调整
const handleResize = () => {
  console.log('窗口大小:', window.innerWidth, window.innerHeight)
}

const throttledResize = throttle(handleResize, 250, {
  leading: true,  // 开始时立即执行
  trailing: false // 结束时不执行
})

// 使用示例
// window.addEventListener('resize', throttledResize)

// ===== 增强版防抖使用示例 =====

// 颜色选择器防抖
const handleColorChange = (color: string) => {
  console.log('颜色改变:', color)
  // 更新UI或发送请求
}

const debouncedColorChange = enhancedDebounce(handleColorChange, 300)

// 使用示例
// debouncedColorChange('#ff0000')
// debouncedColorChange('#00ff00')
// 
// 如果需要取消：
// debouncedColorChange.cancel()
// 
// 如果需要立即执行：
// debouncedColorChange.flush('#0000ff')

// ===== 增强版节流使用示例 =====

// API请求节流
const handleApiRequest = (data: any) => {
  console.log('发送API请求:', data)
  // 发送请求逻辑
}

const throttledApiRequest = enhancedThrottle(handleApiRequest, 1000)

// 使用示例
// throttledApiRequest({ id: 1 })
// throttledApiRequest({ id: 2 }) // 1秒内被节流
// 
// 如果需要取消：
// throttledApiRequest.cancel()

// ===== 实际应用场景 =====

// 场景1: 表单输入验证
export const createFormValidator = (validateFn: (value: string) => boolean, delay = 500) => {
  return debounce(validateFn, delay)
}

// 场景2: 自动保存功能
export const createAutoSave = (saveFn: (data: any) => Promise<void>, delay = 2000) => {
  return debounce(saveFn, delay)
}

// 场景3: 滚动加载更多
export const createScrollLoader = (loadFn: () => void, threshold = 100) => {
  return throttle(loadFn, threshold)
}

// 场景4: 搜索建议
export const createSearchSuggestion = (searchFn: (query: string) => void, delay = 300) => {
  return debounce(searchFn, delay)
}

// Vue 3 Composition API 中的使用示例
/*
import { ref, watch } from 'vue'
import { debounce } from '@shared/utils'

export function useSearch() {
  const searchQuery = ref('')
  const searchResults = ref([])
  
  const performSearch = async (query: string) => {
    if (!query.trim()) {
      searchResults.value = []
      return
    }
    
    try {
      const results = await searchApi(query)
      searchResults.value = results
    } catch (error) {
      console.error('搜索失败:', error)
    }
  }
  
  const debouncedSearch = debounce(performSearch, 300)
  
  watch(searchQuery, (newQuery) => {
    debouncedSearch(newQuery)
  })
  
  return {
    searchQuery,
    searchResults
  }
}
*/
